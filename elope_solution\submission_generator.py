"""
Submission file generator for ELOPE challenge.
Creates JSON files in the required format for Kelvins platform.
"""

import json
import numpy as np
from typing import Dict, List
from pathlib import Path


class SubmissionGenerator:
    """
    Generates submission files in the required JSON format.
    """
    
    def __init__(self):
        """Initialize submission generator."""
        pass
    
    def create_submission(self, 
                         velocity_predictions: Dict[int, Dict[str, List[float]]],
                         output_path: str = "submission.json") -> None:
        """
        Create submission file from velocity predictions.
        
        Args:
            velocity_predictions: Dictionary mapping sequence_id to velocity components
                                Format: {seq_id: {"vx": [...], "vy": [...], "vz": [...]}}
            output_path: Path to save the submission file
        """
        # Validate predictions format
        self._validate_predictions(velocity_predictions)
        
        # Convert numpy arrays to lists if needed
        submission_data = {}
        for seq_id, velocities in velocity_predictions.items():
            submission_data[str(seq_id)] = {
                "vx": self._ensure_list(velocities["vx"]),
                "vy": self._ensure_list(velocities["vy"]),
                "vz": self._ensure_list(velocities["vz"])
            }
        
        # Save to JSON file
        with open(output_path, 'w') as f:
            json.dump(submission_data, f, indent=2)
        
        print(f"Submission saved to {output_path}")
        print(f"Number of sequences: {len(submission_data)}")
        
        # Validate the created file
        self._validate_submission_file(output_path)
    
    def _ensure_list(self, data) -> List[float]:
        """Convert data to list of floats."""
        if isinstance(data, np.ndarray):
            return data.tolist()
        elif isinstance(data, list):
            return [float(x) for x in data]
        else:
            raise ValueError(f"Invalid data type: {type(data)}")
    
    def _validate_predictions(self, predictions: Dict[int, Dict[str, List[float]]]) -> None:
        """Validate prediction data format."""
        required_test_sequences = list(range(28, 93))  # Test sequences are 28-92
        
        # Check if all required sequences are present
        missing_sequences = set(required_test_sequences) - set(predictions.keys())
        if missing_sequences:
            raise ValueError(f"Missing sequences: {missing_sequences}")
        
        # Check for extra sequences
        extra_sequences = set(predictions.keys()) - set(required_test_sequences)
        if extra_sequences:
            raise ValueError(f"Extra sequences found: {extra_sequences}")
        
        # Validate each sequence
        for seq_id, velocities in predictions.items():
            # Check required keys
            required_keys = {"vx", "vy", "vz"}
            if set(velocities.keys()) != required_keys:
                raise ValueError(f"Sequence {seq_id}: Expected keys {required_keys}, got {set(velocities.keys())}")
            
            # Check array lengths (should be 120 for each sequence)
            for component in ["vx", "vy", "vz"]:
                if len(velocities[component]) != 120:
                    raise ValueError(f"Sequence {seq_id}, component {component}: Expected 120 values, got {len(velocities[component])}")
                
                # Check for invalid values
                values = np.array(velocities[component])
                if np.any(np.isnan(values)) or np.any(np.isinf(values)):
                    raise ValueError(f"Sequence {seq_id}, component {component}: Contains NaN or Inf values")
    
    def _validate_submission_file(self, file_path: str) -> None:
        """Validate the created submission file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Check structure
            for seq_id_str, velocities in data.items():
                seq_id = int(seq_id_str)
                if seq_id < 28 or seq_id > 92:
                    raise ValueError(f"Invalid sequence ID: {seq_id}")
                
                for component in ["vx", "vy", "vz"]:
                    if component not in velocities:
                        raise ValueError(f"Missing component {component} in sequence {seq_id}")
                    if len(velocities[component]) != 120:
                        raise ValueError(f"Invalid length for {component} in sequence {seq_id}")
            
            print("✓ Submission file validation passed")
            
        except Exception as e:
            raise ValueError(f"Submission file validation failed: {e}")
    
    def create_dummy_submission(self, output_path: str = "dummy_submission.json") -> None:
        """
        Create a dummy submission with random values for testing.
        
        Args:
            output_path: Path to save the dummy submission
        """
        dummy_predictions = {}
        
        for seq_id in range(28, 93):  # Test sequences
            # Generate random velocities (reasonable ranges for lunar landing)
            vx = np.random.uniform(-50, 50, 120).tolist()
            vy = np.random.uniform(-50, 50, 120).tolist()
            vz = np.random.uniform(-100, 10, 120).tolist()  # Mostly downward motion
            
            dummy_predictions[seq_id] = {
                "vx": vx,
                "vy": vy,
                "vz": vz
            }
        
        self.create_submission(dummy_predictions, output_path)
        print(f"Dummy submission created: {output_path}")


def load_submission(file_path: str) -> Dict[int, Dict[str, List[float]]]:
    """
    Load submission from JSON file.
    
    Args:
        file_path: Path to submission file
        
    Returns:
        Dictionary with velocity predictions
    """
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Convert string keys back to integers
    predictions = {}
    for seq_id_str, velocities in data.items():
        predictions[int(seq_id_str)] = velocities
    
    return predictions


def compare_submissions(file1: str, file2: str) -> Dict[str, float]:
    """
    Compare two submission files and compute differences.
    
    Args:
        file1: Path to first submission file
        file2: Path to second submission file
        
    Returns:
        Dictionary with comparison statistics
    """
    sub1 = load_submission(file1)
    sub2 = load_submission(file2)
    
    differences = []
    
    for seq_id in range(28, 93):
        if seq_id in sub1 and seq_id in sub2:
            for component in ["vx", "vy", "vz"]:
                v1 = np.array(sub1[seq_id][component])
                v2 = np.array(sub2[seq_id][component])
                diff = np.mean(np.abs(v1 - v2))
                differences.append(diff)
    
    return {
        "mean_absolute_difference": np.mean(differences),
        "max_absolute_difference": np.max(differences),
        "min_absolute_difference": np.min(differences),
        "std_absolute_difference": np.std(differences)
    }


if __name__ == "__main__":
    # Example usage
    generator = SubmissionGenerator()
    
    # Create a dummy submission for testing
    generator.create_dummy_submission("test_submission.json")
    
    # Validate the submission
    print("Submission created and validated successfully!")
