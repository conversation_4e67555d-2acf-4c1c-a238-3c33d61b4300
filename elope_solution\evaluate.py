"""
Local evaluation script for ELOPE challenge.
Evaluates predictions against ground truth on training data.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
from pathlib import Path

from data_loader import ELOPEDataLoader
from submission_generator import load_submission


class ELOPEEvaluator:
    """
    Evaluates ELOPE predictions using the official scoring metric.
    """
    
    def __init__(self, data_path: str):
        """
        Initialize evaluator.
        
        Args:
            data_path: Path to ELOPE dataset
        """
        self.data_loader = ELOPEDataLoader(data_path)
    
    def compute_sequence_error(self, 
                              predicted_velocities: Dict[str, List[float]],
                              true_velocities: np.ndarray,
                              true_positions: np.ndarray) -> float:
        """
        Compute error for a single sequence using the official metric.
        
        Args:
            predicted_velocities: Predicted velocities {"vx": [...], "vy": [...], "vz": [...]}
            true_velocities: Ground truth velocities (120, 3)
            true_positions: Ground truth positions (120, 3) for altitude normalization
            
        Returns:
            Sequence error score
        """
        # Convert predictions to numpy arrays
        vx_pred = np.array(predicted_velocities["vx"])
        vy_pred = np.array(predicted_velocities["vy"])
        vz_pred = np.array(predicted_velocities["vz"])
        
        # Extract ground truth
        vx_true = true_velocities[:, 0]
        vy_true = true_velocities[:, 1]
        vz_true = true_velocities[:, 2]
        
        # Extract altitude (z-coordinate, negative by convention)
        z_true = true_positions[:, 2]
        
        # Find valid timestamps (non-NaN)
        valid_mask = (~np.isnan(vx_true) & ~np.isnan(vy_true) & ~np.isnan(vz_true) & 
                     ~np.isnan(z_true) & (z_true != 0))
        
        if np.sum(valid_mask) == 0:
            return float('inf')  # No valid data
        
        # Compute velocity errors
        vx_error = vx_pred[valid_mask] - vx_true[valid_mask]
        vy_error = vy_pred[valid_mask] - vy_true[valid_mask]
        vz_error = vz_pred[valid_mask] - vz_true[valid_mask]
        
        # Compute magnitude of velocity error
        velocity_error_magnitude = np.sqrt(vx_error**2 + vy_error**2 + vz_error**2)
        
        # Normalize by altitude (absolute value since z is negative)
        normalized_errors = velocity_error_magnitude / np.abs(z_true[valid_mask])
        
        # Return mean normalized error
        return np.mean(normalized_errors)
    
    def evaluate_submission(self, submission_path: str) -> Dict[str, float]:
        """
        Evaluate a submission file against training data ground truth.
        
        Args:
            submission_path: Path to submission JSON file
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Load submission
        predictions = load_submission(submission_path)
        
        # Get training sequences that overlap with submission
        train_ids = self.data_loader.get_train_sequence_ids()
        submission_ids = set(predictions.keys())
        
        # Find overlapping sequences (for validation)
        overlap_ids = set(train_ids) & submission_ids
        
        if not overlap_ids:
            print("Warning: No overlap between submission and training sequences")
            return {"error": float('inf')}
        
        sequence_errors = []
        detailed_results = {}
        
        for seq_id in overlap_ids:
            # Load ground truth
            sequence_data = self.data_loader.load_sequence(seq_id, "train")
            true_trajectory = sequence_data['trajectory']
            
            # Extract true velocities and positions
            true_velocities = true_trajectory[:, 3:6]  # vx, vy, vz
            true_positions = true_trajectory[:, 0:3]   # x, y, z
            
            # Compute error
            error = self.compute_sequence_error(
                predictions[seq_id], true_velocities, true_positions
            )
            
            sequence_errors.append(error)
            detailed_results[seq_id] = error
            
            print(f"Sequence {seq_id}: Error = {error:.6f}")
        
        # Compute overall metrics
        mean_error = np.mean(sequence_errors)
        std_error = np.std(sequence_errors)
        min_error = np.min(sequence_errors)
        max_error = np.max(sequence_errors)
        
        results = {
            "mean_error": mean_error,
            "std_error": std_error,
            "min_error": min_error,
            "max_error": max_error,
            "num_sequences": len(sequence_errors),
            "detailed_results": detailed_results
        }
        
        return results
    
    def compare_with_baseline(self, submission_path: str) -> Dict[str, float]:
        """
        Compare submission with a simple baseline.
        
        Args:
            submission_path: Path to submission file
            
        Returns:
            Comparison results
        """
        # Create simple baseline (zero velocities)
        baseline_predictions = {}
        test_ids = self.data_loader.get_test_sequence_ids()
        
        for seq_id in test_ids:
            baseline_predictions[seq_id] = {
                "vx": [0.0] * 120,
                "vy": [0.0] * 120,
                "vz": [-10.0] * 120  # Assume constant downward motion
            }
        
        # Save baseline
        from submission_generator import SubmissionGenerator
        generator = SubmissionGenerator()
        baseline_path = "baseline_submission.json"
        generator.create_submission(baseline_predictions, baseline_path)
        
        # Evaluate both
        submission_results = self.evaluate_submission(submission_path)
        baseline_results = self.evaluate_submission(baseline_path)
        
        return {
            "submission_error": submission_results.get("mean_error", float('inf')),
            "baseline_error": baseline_results.get("mean_error", float('inf')),
            "improvement": baseline_results.get("mean_error", float('inf')) - submission_results.get("mean_error", float('inf'))
        }
    
    def plot_velocity_comparison(self, 
                                submission_path: str, 
                                sequence_id: int,
                                output_path: str = None):
        """
        Plot velocity comparison for a specific sequence.
        
        Args:
            submission_path: Path to submission file
            sequence_id: Sequence to plot
            output_path: Path to save plot (optional)
        """
        # Load data
        predictions = load_submission(submission_path)
        
        if sequence_id not in predictions:
            print(f"Sequence {sequence_id} not found in submission")
            return
        
        # Load ground truth if available
        train_ids = self.data_loader.get_train_sequence_ids()
        if sequence_id in train_ids:
            sequence_data = self.data_loader.load_sequence(sequence_id, "train")
            true_trajectory = sequence_data['trajectory']
            true_velocities = true_trajectory[:, 3:6]
            timestamps = sequence_data['timestamps']
        else:
            print(f"Ground truth not available for sequence {sequence_id}")
            true_velocities = None
            timestamps = np.arange(120)
        
        # Extract predictions
        pred_vx = predictions[sequence_id]["vx"]
        pred_vy = predictions[sequence_id]["vy"]
        pred_vz = predictions[sequence_id]["vz"]
        
        # Create plot
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        components = ['vx', 'vy', 'vz']
        pred_data = [pred_vx, pred_vy, pred_vz]
        
        for i, (ax, component, pred) in enumerate(zip(axes, components, pred_data)):
            ax.plot(timestamps, pred, 'b-', label='Predicted', linewidth=2)
            
            if true_velocities is not None:
                true_comp = true_velocities[:, i]
                valid_mask = ~np.isnan(true_comp)
                ax.plot(timestamps[valid_mask], true_comp[valid_mask], 'r-', 
                       label='Ground Truth', linewidth=2)
            
            ax.set_ylabel(f'{component} [m/s]')
            ax.set_title(f'Velocity Component {component.upper()} - Sequence {sequence_id}')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        axes[-1].set_xlabel('Time [s]')
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {output_path}")
        else:
            plt.show()


def main():
    """Main evaluation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Evaluate ELOPE submission")
    parser.add_argument("--data_path", type=str, default=".", 
                       help="Path to ELOPE dataset")
    parser.add_argument("--submission", type=str, required=True,
                       help="Path to submission file")
    parser.add_argument("--plot_sequence", type=int, default=None,
                       help="Sequence ID to plot (optional)")
    
    args = parser.parse_args()
    
    # Initialize evaluator
    evaluator = ELOPEEvaluator(args.data_path)
    
    # Evaluate submission
    print("Evaluating submission...")
    results = evaluator.evaluate_submission(args.submission)
    
    print("\n" + "="*50)
    print("EVALUATION RESULTS")
    print("="*50)
    print(f"Mean Error: {results.get('mean_error', 'N/A'):.6f}")
    print(f"Std Error:  {results.get('std_error', 'N/A'):.6f}")
    print(f"Min Error:  {results.get('min_error', 'N/A'):.6f}")
    print(f"Max Error:  {results.get('max_error', 'N/A'):.6f}")
    print(f"Sequences:  {results.get('num_sequences', 'N/A')}")
    
    # Compare with baseline
    print("\nComparing with baseline...")
    comparison = evaluator.compare_with_baseline(args.submission)
    print(f"Submission Error: {comparison['submission_error']:.6f}")
    print(f"Baseline Error:   {comparison['baseline_error']:.6f}")
    print(f"Improvement:      {comparison['improvement']:.6f}")
    
    # Plot specific sequence if requested
    if args.plot_sequence is not None:
        print(f"\nPlotting sequence {args.plot_sequence}...")
        evaluator.plot_velocity_comparison(
            args.submission, 
            args.plot_sequence,
            f"velocity_comparison_seq_{args.plot_sequence}.png"
        )


if __name__ == "__main__":
    main()
