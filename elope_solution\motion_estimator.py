"""
Motion estimation module for ELOPE challenge.
Estimates camera motion from optical flow and multi-sensor data.
"""

import numpy as np
import cv2
from typing import Tuple, List, Dict, Optional
from scipy.optimize import minimize
from scipy.spatial.transform import Rotation


class OpticalFlowEstimator:
    """
    Estimates optical flow from event frames.
    """
    
    def __init__(self, method: str = "lucas_kanade"):
        """
        Initialize optical flow estimator.

        Args:
            method: Optical flow method ("farneback", "lucas_kanade")
        """
        self.method = method
        # Use Lucas-Kanade by default for speed
        if method == "farneback":
            print("Warning: Using Farneback (slower method)")
        else:
            print("Using Lucas-Kanade optical flow (faster)")
    
    def compute_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> np.ndarray:
        """
        Compute optical flow between two frames.
        
        Args:
            frame1: First frame
            frame2: Second frame
            
        Returns:
            Optical flow field
        """
        # Convert to grayscale if needed
        if len(frame1.shape) == 3:
            gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        else:
            gray1 = frame1.copy()
            
        if len(frame2.shape) == 3:
            gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        else:
            gray2 = frame2.copy()
        
        if self.method == "farneback":
            return self._farneback_flow(gray1, gray2)
        elif self.method == "lucas_kanade":
            return self._lucas_kanade_flow(gray1, gray2)
        else:
            raise ValueError(f"Unknown method: {self.method}")
    
    def _farneback_flow(self, gray1: np.ndarray, gray2: np.ndarray) -> np.ndarray:
        """Compute Farneback optical flow."""
        flow = cv2.calcOpticalFlowFarneback(
            gray1, gray2, None,
            pyr_scale=0.5,
            levels=3,
            winsize=15,
            iterations=3,
            poly_n=5,
            poly_sigma=1.2,
            flags=0
        )
        return flow
    
    def _lucas_kanade_flow(self, gray1: np.ndarray, gray2: np.ndarray) -> np.ndarray:
        """Compute Lucas-Kanade optical flow."""
        # Detect corners in first frame (optimized for speed)
        corners = cv2.goodFeaturesToTrack(
            gray1,
            maxCorners=100,  # Reduced from 1000 to 100
            qualityLevel=0.05,  # Increased threshold for faster detection
            minDistance=15,  # Increased distance to reduce points
            blockSize=5  # Slightly larger block
        )
        
        if corners is None or len(corners) == 0:
            return np.zeros((gray1.shape[0], gray1.shape[1], 2))
        
        # Calculate optical flow (optimized parameters)
        next_corners, status, _ = cv2.calcOpticalFlowPyrLK(
            gray1, gray2, corners, None,
            winSize=(10, 10),  # Reduced window size
            maxLevel=1,  # Reduced pyramid levels
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 5, 0.1)  # Fewer iterations
        )
        
        # Create dense flow field from sparse flow
        flow = np.zeros((gray1.shape[0], gray1.shape[1], 2))
        
        if next_corners is not None and status is not None:
            good_mask = status.flatten() == 1
            good_corners = corners[good_mask]
            good_next = next_corners[good_mask]

            for corner, next_corner in zip(good_corners, good_next):
                x, y = int(corner[0][0]), int(corner[0][1])
                dx, dy = next_corner[0][0] - corner[0][0], next_corner[0][1] - corner[0][1]

                if 0 <= x < flow.shape[1] and 0 <= y < flow.shape[0]:
                    flow[y, x] = [dx, dy]
        
        return flow


class MotionEstimator:
    """
    Estimates camera motion from optical flow and sensor data.
    """
    
    def __init__(self, camera_matrix: Optional[np.ndarray] = None):
        """
        Initialize motion estimator.
        
        Args:
            camera_matrix: Camera intrinsic matrix (3x3)
        """
        # Default camera matrix for 200x200 image
        if camera_matrix is None:
            self.camera_matrix = np.array([
                [100, 0, 100],
                [0, 100, 100],
                [0, 0, 1]
            ], dtype=np.float32)
        else:
            self.camera_matrix = camera_matrix
    
    def estimate_motion_from_flow(self, 
                                 flow: np.ndarray,
                                 depth_estimate: float = 1000.0) -> Dict[str, np.ndarray]:
        """
        Estimate camera motion from optical flow.
        
        Args:
            flow: Optical flow field
            depth_estimate: Estimated scene depth
            
        Returns:
            Dictionary containing estimated motion parameters
        """
        # Extract flow vectors
        flow_vectors = self._extract_flow_vectors(flow)
        
        if len(flow_vectors) < 8:  # Need minimum points for estimation
            return {
                'translation': np.zeros(3),
                'rotation': np.zeros(3),
                'confidence': 0.0
            }
        
        # Estimate motion using essential matrix
        translation, rotation, confidence = self._estimate_motion_essential(
            flow_vectors, depth_estimate
        )
        
        return {
            'translation': translation,
            'rotation': rotation,
            'confidence': confidence
        }
    
    def _extract_flow_vectors(self, flow: np.ndarray) -> List[Tuple[np.ndarray, np.ndarray]]:
        """Extract significant flow vectors from flow field."""
        vectors = []

        # Check if flow is valid
        if flow is None or flow.size == 0:
            return vectors

        # Find pixels with significant flow
        flow_magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)

        # Check for valid flow values
        valid_flow = np.isfinite(flow_magnitude) & (flow_magnitude > 0)
        if not np.any(valid_flow):
            return vectors

        threshold = np.percentile(flow_magnitude[valid_flow], 75)

        y_coords, x_coords = np.where((flow_magnitude > threshold) & valid_flow)

        # Limit number of points for performance (ultra-optimized)
        max_points = 200  # Reduced from 1000 to 200 for speed
        if len(y_coords) > max_points:
            indices = np.random.choice(len(y_coords), max_points, replace=False)
            y_coords = y_coords[indices]
            x_coords = x_coords[indices]

        for y, x in zip(y_coords, x_coords):
            if flow_magnitude[y, x] > 0:
                p1 = np.array([x, y], dtype=np.float32)
                p2 = p1 + flow[y, x]
                # Check if p2 is valid
                if np.all(np.isfinite(p2)):
                    vectors.append((p1, p2))

        return vectors
    
    def _estimate_motion_essential(self,
                                  flow_vectors: List[Tuple[np.ndarray, np.ndarray]],
                                  depth: float) -> Tuple[np.ndarray, np.ndarray, float]:
        """Estimate motion using essential matrix decomposition."""
        if len(flow_vectors) < 8:
            return np.zeros(3), np.zeros(3), 0.0

        try:
            # Convert to normalized coordinates
            points1 = np.array([v[0] for v in flow_vectors], dtype=np.float32)
            points2 = np.array([v[1] for v in flow_vectors], dtype=np.float32)

            # Check for valid points
            if points1.shape[0] < 8 or points2.shape[0] < 8:
                return np.zeros(3), np.zeros(3), 0.0

            # Estimate essential matrix
            E, mask = cv2.findEssentialMat(
                points1, points2, self.camera_matrix,
                method=cv2.RANSAC,
                prob=0.999,
                threshold=1.0
            )

            if E is None or E.size == 0:
                return np.zeros(3), np.zeros(3), 0.0

            # Decompose essential matrix
            retval, R, t, mask_pose = cv2.recoverPose(E, points1, points2, self.camera_matrix)

            if retval < 4:  # Need at least 4 points for valid pose
                return np.zeros(3), np.zeros(3), 0.0

            # Convert rotation matrix to rotation vector
            rotation_vector, _ = cv2.Rodrigues(R)

            # Scale translation by depth estimate
            translation = t.flatten() * depth

            # Calculate confidence based on inlier ratio
            confidence = np.sum(mask) / len(mask) if mask is not None else 0.0

            return translation, rotation_vector.flatten(), confidence

        except Exception as e:
            print(f"Error in motion estimation: {e}")
            return np.zeros(3), np.zeros(3), 0.0


class MultiSensorFusion:
    """
    Fuses motion estimates from multiple sensors.
    """
    
    def __init__(self):
        """Initialize multi-sensor fusion."""
        pass
    
    def fuse_motion_estimates(self,
                             optical_flow_motion: Dict[str, np.ndarray],
                             imu_data: Dict[str, np.ndarray],
                             range_data: Optional[Dict[str, np.ndarray]] = None,
                             weights: Optional[Dict[str, float]] = None) -> Dict[str, np.ndarray]:
        """
        Fuse motion estimates from different sensors.
        
        Args:
            optical_flow_motion: Motion from optical flow
            imu_data: IMU angular velocity data
            range_data: Range meter data (optional)
            weights: Fusion weights for different sensors
            
        Returns:
            Fused motion estimate
        """
        if weights is None:
            weights = {'optical_flow': 0.7, 'imu': 0.3}
        
        # Start with optical flow estimate
        fused_translation = optical_flow_motion['translation'] * weights['optical_flow']
        fused_rotation = optical_flow_motion['rotation'] * weights['optical_flow']
        
        # Add IMU contribution for rotation
        if 'angular_velocity' in imu_data:
            imu_rotation = imu_data['angular_velocity']
            fused_rotation += imu_rotation * weights['imu']
        
        # Use range data to scale translation if available
        if range_data is not None and 'depth_change' in range_data:
            depth_scale = range_data['depth_change']
            if depth_scale != 0:
                fused_translation *= abs(depth_scale) / np.linalg.norm(fused_translation)
        
        # Calculate confidence
        confidence = optical_flow_motion.get('confidence', 0.5)
        
        return {
            'translation': fused_translation,
            'rotation': fused_rotation,
            'confidence': confidence
        }
    
    def process_imu_data(self, 
                        trajectory: np.ndarray, 
                        timestamps: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Process IMU data from trajectory.
        
        Args:
            trajectory: Trajectory data with angular velocities
            timestamps: Corresponding timestamps
            
        Returns:
            Processed IMU data
        """
        # Extract angular velocities (columns 9, 10, 11)
        angular_velocities = trajectory[:, 9:12]
        
        # Remove NaN values and interpolate if needed
        valid_mask = ~np.isnan(angular_velocities).any(axis=1)
        
        if np.sum(valid_mask) > 0:
            # Use valid angular velocity data
            avg_angular_velocity = np.mean(angular_velocities[valid_mask], axis=0)
        else:
            avg_angular_velocity = np.zeros(3)
        
        return {
            'angular_velocity': avg_angular_velocity,
            'timestamps': timestamps
        }
    
    def process_range_data(self, 
                          range_meter: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Process range meter data.
        
        Args:
            range_meter: Range meter data [timestamp, distance]
            
        Returns:
            Processed range data
        """
        if len(range_meter) < 2:
            return {'depth_change': 0.0}
        
        # Calculate depth change rate
        time_diff = range_meter[-1, 0] - range_meter[0, 0]
        depth_diff = range_meter[-1, 1] - range_meter[0, 1]
        
        depth_change_rate = depth_diff / time_diff if time_diff > 0 else 0.0
        
        return {
            'depth_change': depth_change_rate,
            'timestamps': range_meter[:, 0],
            'distances': range_meter[:, 1]
        }
