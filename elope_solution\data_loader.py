"""
Data loading and preprocessing module for ELOPE challenge.
"""

import numpy as np
import pandas as pd
import os
from typing import Dict, Tuple, List, Optional
from pathlib import Path


class ELOPEDataLoader:
    """
    Data loader for ELOPE dataset.
    Handles loading .npz files and preprocessing event data.
    """
    
    def __init__(self, data_path: str):
        """
        Initialize data loader.
        
        Args:
            data_path: Path to the ELOPE dataset directory
        """
        self.data_path = Path(data_path)
        self.train_path = self.data_path / "train"
        self.test_path = self.data_path / "test"
        
        # Verify paths exist
        if not self.data_path.exists():
            raise FileNotFoundError(f"Data path {data_path} does not exist")
        if not self.train_path.exists():
            raise FileNotFoundError(f"Train path {self.train_path} does not exist")
        if not self.test_path.exists():
            raise FileNotFoundError(f"Test path {self.test_path} does not exist")
    
    def load_sequence(self, sequence_id: int, split: str = "train") -> Dict:
        """
        Load a single sequence from the dataset.
        
        Args:
            sequence_id: Sequence ID (e.g., 0, 1, 28, etc.)
            split: Either "train" or "test"
            
        Returns:
            Dictionary containing events, timestamps, trajectory, and range_meter data
        """
        if split == "train":
            file_path = self.train_path / f"{sequence_id:04d}.npz"
        elif split == "test":
            file_path = self.test_path / f"{sequence_id:04d}.npz"
        else:
            raise ValueError("Split must be either 'train' or 'test'")
        
        if not file_path.exists():
            raise FileNotFoundError(f"Sequence file {file_path} does not exist")
        
        # Load the .npz file
        sequence = np.load(file_path)
        
        return {
            'events': sequence['events'],
            'timestamps': sequence['timestamps'],
            'trajectory': sequence['traj'],
            'range_meter': sequence['range_meter']
        }
    
    def events_to_dataframe(self, events: np.ndarray) -> pd.DataFrame:
        """
        Convert events array to pandas DataFrame for easier manipulation.
        
        Args:
            events: Numpy array of events with dtype [('x', 'u1'), ('y', 'u1'), ('p', '?'), ('t', '<u8')]
            
        Returns:
            DataFrame with columns ['x', 'y', 'p', 't']
        """
        return pd.DataFrame(events, columns=['x', 'y', 'p', 't'])
    
    def get_train_sequence_ids(self) -> List[int]:
        """Get list of available training sequence IDs."""
        files = list(self.train_path.glob("*.npz"))
        return sorted([int(f.stem) for f in files])
    
    def get_test_sequence_ids(self) -> List[int]:
        """Get list of available test sequence IDs."""
        files = list(self.test_path.glob("*.npz"))
        return sorted([int(f.stem) for f in files])
    
    def load_all_sequences(self, split: str = "train") -> Dict[int, Dict]:
        """
        Load all sequences from a split.
        
        Args:
            split: Either "train" or "test"
            
        Returns:
            Dictionary mapping sequence_id to sequence data
        """
        if split == "train":
            sequence_ids = self.get_train_sequence_ids()
        else:
            sequence_ids = self.get_test_sequence_ids()
        
        sequences = {}
        for seq_id in sequence_ids:
            sequences[seq_id] = self.load_sequence(seq_id, split)
        
        return sequences


def preprocess_events(events_df: pd.DataFrame, 
                     start_time: Optional[float] = None,
                     end_time: Optional[float] = None) -> pd.DataFrame:
    """
    Preprocess events data with optional time filtering.
    
    Args:
        events_df: DataFrame with event data
        start_time: Start time in microseconds (optional)
        end_time: End time in microseconds (optional)
        
    Returns:
        Filtered and preprocessed events DataFrame
    """
    # Convert time to seconds for easier handling
    events_df = events_df.copy()
    events_df['t_sec'] = events_df['t'] / 1e6
    
    # Apply time filtering if specified
    if start_time is not None:
        events_df = events_df[events_df['t'] >= start_time]
    if end_time is not None:
        events_df = events_df[events_df['t'] <= end_time]
    
    # Sort by timestamp
    events_df = events_df.sort_values('t').reset_index(drop=True)
    
    return events_df


def interpolate_trajectory(timestamps: np.ndarray, 
                          trajectory: np.ndarray, 
                          target_times: np.ndarray) -> np.ndarray:
    """
    Interpolate trajectory data to target timestamps.
    
    Args:
        timestamps: Original timestamps
        trajectory: Original trajectory data
        target_times: Target timestamps for interpolation
        
    Returns:
        Interpolated trajectory data
    """
    from scipy.interpolate import interp1d
    
    # Only interpolate non-NaN columns
    interpolated = np.full((len(target_times), trajectory.shape[1]), np.nan)
    
    for col in range(trajectory.shape[1]):
        if not np.isnan(trajectory[:, col]).all():
            # Find valid (non-NaN) data points
            valid_mask = ~np.isnan(trajectory[:, col])
            if np.sum(valid_mask) > 1:  # Need at least 2 points for interpolation
                f = interp1d(timestamps[valid_mask], trajectory[valid_mask, col], 
                           kind='linear', bounds_error=False, fill_value=np.nan)
                interpolated[:, col] = f(target_times)
    
    return interpolated
