#!/usr/bin/env python3
"""
Development Framework for ELOPE solution.
Implements staged validation: 0-10 (dev) -> 0-27 (full train) -> 28-92 (test)
"""

import sys
import os
import json
import numpy as np
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import ELOPESolver
from evaluate import ELOPEEvaluator

class DevelopmentFramework:
    """
    Staged development framework:
    1. Dev set (0-10): Quick iteration and testing
    2. Train set (0-27): Full training validation  
    3. Test set (28-92): Final submission
    """
    
    def __init__(self, data_path: str = ".."):
        """Initialize the development framework."""
        self.data_path = data_path
        self.solver = ELOPESolver(data_path)
        self.evaluator = ELOPEEvaluator(data_path)
        
        # Define sequence sets
        self.dev_sequences = list(range(0, 11))      # 0-10: Quick development
        self.train_sequences = list(range(0, 28))    # 0-27: Full training
        self.test_sequences = list(range(28, 93))    # 28-92: Final test
        
        print(f"Development sequences (0-10): {len(self.dev_sequences)} sequences")
        print(f"Training sequences (0-27): {len(self.train_sequences)} sequences") 
        print(f"Test sequences (28-92): {len(self.test_sequences)} sequences")
        
        # Performance tracking
        self.performance_log = self._load_performance_log()
        
    def _load_performance_log(self) -> Dict[str, Any]:
        """Load existing performance log or create new one."""
        log_file = "performance_log.json"
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                return json.load(f)
        else:
            return {
                'baseline_recorded': False,
                'versions': [],
                'best_dev_score': float('inf'),
                'best_train_score': float('inf'),
                'best_test_score': float('inf')
            }
    
    def _save_performance_log(self):
        """Save performance log to file."""
        with open("performance_log.json", 'w') as f:
            json.dump(self.performance_log, f, indent=2)
    
    def record_baseline(self) -> Dict[str, float]:
        """Record baseline performance on all sets."""
        print("Recording baseline performance...")
        
        if self.performance_log['baseline_recorded']:
            print("Baseline already recorded. Use record_version() for new versions.")
            return self.performance_log['versions'][0] if self.performance_log['versions'] else {}
        
        baseline_results = {
            'version': 'baseline',
            'timestamp': datetime.now().isoformat(),
            'dev_score': self._validate_on_sequences(self.dev_sequences, "dev"),
            'train_score': self._validate_on_sequences(self.train_sequences, "train"),
            'description': 'Initial baseline performance'
        }
        
        self.performance_log['baseline_recorded'] = True
        self.performance_log['versions'].append(baseline_results)
        self.performance_log['best_dev_score'] = baseline_results['dev_score']
        self.performance_log['best_train_score'] = baseline_results['train_score']
        
        self._save_performance_log()
        self._print_results("BASELINE", baseline_results)
        
        return baseline_results
    
    def validate_dev_set(self) -> Dict[str, float]:
        """Quick validation on development set (0-10)."""
        print("Validating on development set (sequences 0-10)...")
        
        start_time = time.time()
        dev_score = self._validate_on_sequences(self.dev_sequences, "dev")
        validation_time = time.time() - start_time
        
        results = {
            'dev_score': dev_score,
            'validation_time': validation_time,
            'num_sequences': len(self.dev_sequences)
        }
        
        print(f"Dev set validation completed in {validation_time:.1f}s")
        print(f"Dev score: {dev_score:.6f}")
        
        # Check if this is better than previous best
        if dev_score < self.performance_log['best_dev_score']:
            improvement = self.performance_log['best_dev_score'] - dev_score
            print(f"🎉 New best dev score! Improvement: {improvement:.6f}")
            self.performance_log['best_dev_score'] = dev_score
            self._save_performance_log()
        
        return results
    
    def validate_train_set(self) -> Dict[str, float]:
        """Full validation on training set (0-27)."""
        print("Validating on full training set (sequences 0-27)...")
        
        start_time = time.time()
        train_score = self._validate_on_sequences(self.train_sequences, "train")
        validation_time = time.time() - start_time
        
        results = {
            'train_score': train_score,
            'validation_time': validation_time,
            'num_sequences': len(self.train_sequences)
        }
        
        print(f"Train set validation completed in {validation_time:.1f}s")
        print(f"Train score: {train_score:.6f}")
        
        # Check if this is better than previous best
        if train_score < self.performance_log['best_train_score']:
            improvement = self.performance_log['best_train_score'] - train_score
            print(f"🎉 New best train score! Improvement: {improvement:.6f}")
            self.performance_log['best_train_score'] = train_score
            self._save_performance_log()
        
        return results
    
    def generate_test_submission(self, output_file: str = "submission.json") -> str:
        """Generate submission on test set (28-92)."""
        print("Generating predictions on test set (sequences 28-92)...")
        
        start_time = time.time()
        
        # Generate predictions for test sequences
        predictions = {}
        for seq_id in self.test_sequences:
            print(f"Processing test sequence {seq_id}...")
            sequence_data = self.solver.data_loader.load_sequence(seq_id, "test")
            estimated_velocities = self.solver.process_sequence(sequence_data, seq_id)
            predictions[seq_id] = estimated_velocities
        
        # Save submission file
        self.solver.submission_generator.create_submission(predictions, output_file)
        
        processing_time = time.time() - start_time
        
        print(f"Test submission generated in {processing_time:.1f}s")
        print(f"Submission saved to: {output_file}")
        
        return output_file
    
    def record_version(self, version_name: str, description: str = "") -> Dict[str, Any]:
        """Record performance of a new version."""
        print(f"Recording version: {version_name}")
        
        version_results = {
            'version': version_name,
            'timestamp': datetime.now().isoformat(),
            'description': description,
            'dev_score': self._validate_on_sequences(self.dev_sequences, "dev"),
            'train_score': self._validate_on_sequences(self.train_sequences, "train")
        }
        
        self.performance_log['versions'].append(version_results)
        
        # Update best scores
        if version_results['dev_score'] < self.performance_log['best_dev_score']:
            self.performance_log['best_dev_score'] = version_results['dev_score']
        
        if version_results['train_score'] < self.performance_log['best_train_score']:
            self.performance_log['best_train_score'] = version_results['train_score']
        
        self._save_performance_log()
        self._print_results(version_name.upper(), version_results)
        
        return version_results
    
    def _validate_on_sequences(self, sequence_ids: List[int], set_name: str) -> float:
        """Validate on specified sequences and return mean error."""
        predictions = {}
        
        for seq_id in sequence_ids:
            sequence_data = self.solver.data_loader.load_sequence(seq_id, "train")
            estimated_velocities = self.solver.process_sequence(sequence_data, seq_id)
            predictions[seq_id] = estimated_velocities
        
        # Save predictions to temporary file
        temp_file = f"temp_{set_name}_predictions.json"
        json_predictions = {str(k): v for k, v in predictions.items()}
        
        with open(temp_file, 'w') as f:
            json.dump(json_predictions, f, indent=2)
        
        # Evaluate
        results = self.evaluator.evaluate_submission(temp_file)
        
        # Clean up temp file
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        return results.get('mean_error', float('inf'))
    
    def _print_results(self, version_name: str, results: Dict[str, Any]):
        """Print formatted results."""
        print("\n" + "="*60)
        print(f"{version_name} PERFORMANCE RESULTS")
        print("="*60)
        print(f"Version:      {results['version']}")
        print(f"Timestamp:    {results['timestamp']}")
        print(f"Dev Score:    {results['dev_score']:.6f}")
        print(f"Train Score:  {results['train_score']:.6f}")
        if results.get('description'):
            print(f"Description:  {results['description']}")
        print("="*60)
    
    def show_performance_history(self):
        """Show performance history and trends."""
        if not self.performance_log['versions']:
            print("No performance history available.")
            return
        
        print("\n" + "="*80)
        print("PERFORMANCE HISTORY")
        print("="*80)
        
        print(f"{'Version':<15} {'Dev Score':<12} {'Train Score':<12} {'Dev Δ':<10} {'Train Δ':<10}")
        print("-" * 80)
        
        prev_dev = None
        prev_train = None
        
        for version in self.performance_log['versions']:
            dev_score = version['dev_score']
            train_score = version['train_score']
            
            dev_delta = ""
            train_delta = ""
            
            if prev_dev is not None:
                dev_change = dev_score - prev_dev
                train_change = train_score - prev_train
                dev_delta = f"{dev_change:+.4f}"
                train_delta = f"{train_change:+.4f}"
            
            print(f"{version['version']:<15} {dev_score:<12.6f} {train_score:<12.6f} {dev_delta:<10} {train_delta:<10}")
            
            prev_dev = dev_score
            prev_train = train_score
        
        print("-" * 80)
        print(f"Best Dev Score:   {self.performance_log['best_dev_score']:.6f}")
        print(f"Best Train Score: {self.performance_log['best_train_score']:.6f}")
        print("="*80)
    
    def compare_with_baseline(self) -> Dict[str, float]:
        """Compare current performance with baseline."""
        if not self.performance_log['versions']:
            print("No baseline recorded yet.")
            return {}
        
        baseline = self.performance_log['versions'][0]
        current_dev = self.validate_dev_set()['dev_score']
        current_train = self.validate_train_set()['train_score']
        
        dev_improvement = baseline['dev_score'] - current_dev
        train_improvement = baseline['train_score'] - current_train
        
        print("\n" + "="*60)
        print("COMPARISON WITH BASELINE")
        print("="*60)
        print(f"Baseline Dev:     {baseline['dev_score']:.6f}")
        print(f"Current Dev:      {current_dev:.6f}")
        print(f"Dev Improvement:  {dev_improvement:+.6f}")
        print()
        print(f"Baseline Train:   {baseline['train_score']:.6f}")
        print(f"Current Train:    {current_train:.6f}")
        print(f"Train Improvement: {train_improvement:+.6f}")
        print("="*60)
        
        return {
            'dev_improvement': dev_improvement,
            'train_improvement': train_improvement,
            'current_dev': current_dev,
            'current_train': current_train
        }

def main():
    """Main function for development framework."""
    print("ELOPE Development Framework")
    print("Staged validation: Dev (0-10) -> Train (0-27) -> Test (28-92)")
    print("="*60)
    
    framework = DevelopmentFramework()
    
    # Check if baseline exists
    if not framework.performance_log['baseline_recorded']:
        print("\n📊 Recording baseline performance...")
        framework.record_baseline()
    else:
        print("\n📊 Baseline already recorded.")
        framework.show_performance_history()
    
    print("\n🚀 Development framework ready!")
    print("\nAvailable commands:")
    print("- framework.validate_dev_set()     # Quick validation (0-10)")
    print("- framework.validate_train_set()   # Full validation (0-27)")
    print("- framework.record_version(name)   # Record new version")
    print("- framework.show_performance_history()  # Show history")
    print("- framework.generate_test_submission()  # Generate final submission")

if __name__ == "__main__":
    main()
