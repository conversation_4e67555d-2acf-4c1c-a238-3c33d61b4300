"""
Feature extraction module for ELOPE challenge.
Converts event data to image frames and extracts visual features.
"""

import numpy as np
import pandas as pd
import cv2
from typing import Tuple, List, Optional
import matplotlib.pyplot as plt


class EventFrameGenerator:
    """
    Generates image frames from event data for optical flow computation.
    """
    
    def __init__(self, width: int = 200, height: int = 200):
        """
        Initialize event frame generator.
        
        Args:
            width: Image width (default 200 for ELOPE dataset)
            height: Image height (default 200 for ELOPE dataset)
        """
        self.width = width
        self.height = height
    
    def events_to_frame(self, 
                       events_df: pd.DataFrame, 
                       start_time: float, 
                       duration: float,
                       method: str = "latest") -> np.ndarray:
        """
        Convert events to a single image frame.
        
        Args:
            events_df: DataFrame with event data
            start_time: Start time in microseconds
            duration: Duration in microseconds
            method: Method for frame generation ("latest", "count", "time_surface")
            
        Returns:
            Generated frame as numpy array
        """
        end_time = start_time + duration
        
        # Filter events in time window
        mask = (events_df['t'] >= start_time) & (events_df['t'] < end_time)
        window_events = events_df[mask]
        
        if len(window_events) == 0:
            return np.zeros((self.height, self.width, 3), dtype=np.uint8)
        
        if method == "latest":
            return self._latest_event_frame(window_events)
        elif method == "count":
            return self._count_frame(window_events)
        elif method == "time_surface":
            return self._time_surface_frame(window_events, start_time, end_time)
        else:
            raise ValueError(f"Unknown method: {method}")
    
    def _latest_event_frame(self, events_df: pd.DataFrame) -> np.ndarray:
        """
        Generate frame showing latest event at each pixel.
        Similar to the starter kit implementation.
        """
        # Get latest event at each pixel location
        latest_events = events_df.loc[events_df.groupby(['x', 'y'])['t'].idxmax()]
        
        # Create empty frame
        frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
        
        # Separate positive and negative polarity events
        pos_events = latest_events[latest_events['p'] == True]
        neg_events = latest_events[latest_events['p'] == False]
        
        # Set colors: white for positive, blue for negative
        if len(pos_events) > 0:
            frame[pos_events['y'], pos_events['x']] = [255, 255, 255]
        if len(neg_events) > 0:
            frame[neg_events['y'], neg_events['x']] = [80, 137, 204]
        
        return frame
    
    def _count_frame(self, events_df: pd.DataFrame) -> np.ndarray:
        """
        Generate frame based on event count at each pixel.
        """
        # Count events at each pixel
        pos_counts = events_df[events_df['p'] == True].groupby(['x', 'y']).size()
        neg_counts = events_df[events_df['p'] == False].groupby(['x', 'y']).size()
        
        # Create frame
        frame = np.zeros((self.height, self.width), dtype=np.float32)
        
        # Add positive events (positive values)
        for (x, y), count in pos_counts.items():
            if 0 <= x < self.width and 0 <= y < self.height:
                frame[y, x] += count
        
        # Subtract negative events (negative values)
        for (x, y), count in neg_counts.items():
            if 0 <= x < self.width and 0 <= y < self.height:
                frame[y, x] -= count
        
        # Normalize to [0, 255]
        if frame.max() > frame.min():
            frame = (frame - frame.min()) / (frame.max() - frame.min()) * 255
        
        return frame.astype(np.uint8)
    
    def _time_surface_frame(self, events_df: pd.DataFrame, 
                           start_time: float, end_time: float) -> np.ndarray:
        """
        Generate time surface representation.
        """
        # Get latest timestamp at each pixel
        latest_times = events_df.groupby(['x', 'y'])['t'].max()
        
        # Create time surface
        time_surface = np.zeros((self.height, self.width), dtype=np.float32)
        
        for (x, y), t in latest_times.items():
            if 0 <= x < self.width and 0 <= y < self.height:
                # Normalize time to [0, 1] within the window
                normalized_time = (t - start_time) / (end_time - start_time)
                time_surface[y, x] = normalized_time
        
        # Convert to grayscale image
        return (time_surface * 255).astype(np.uint8)
    
    def generate_frame_sequence(self, 
                               events_df: pd.DataFrame,
                               time_window: float = 50000,  # 50ms in microseconds
                               overlap: float = 0.5,
                               method: str = "latest") -> List[Tuple[np.ndarray, float]]:
        """
        Generate a sequence of frames from events.
        
        Args:
            events_df: DataFrame with event data
            time_window: Duration of each frame in microseconds
            overlap: Overlap between consecutive frames (0-1)
            method: Frame generation method
            
        Returns:
            List of (frame, timestamp) tuples
        """
        if len(events_df) == 0:
            return []
        
        start_time = events_df['t'].min()
        end_time = events_df['t'].max()
        
        step = time_window * (1 - overlap)
        frames = []
        
        current_time = start_time
        while current_time + time_window <= end_time:
            frame = self.events_to_frame(events_df, current_time, time_window, method)
            frames.append((frame, current_time))
            current_time += step
        
        return frames


def extract_optical_flow_features(frame1: np.ndarray, frame2: np.ndarray) -> dict:
    """
    Extract optical flow features between two frames.
    
    Args:
        frame1: First frame
        frame2: Second frame
        
    Returns:
        Dictionary containing flow features
    """
    # Convert to grayscale if needed
    if len(frame1.shape) == 3:
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
    else:
        gray1 = frame1
        
    if len(frame2.shape) == 3:
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
    else:
        gray2 = frame2
    
    # Compute optical flow using Farneback method
    flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None)
    
    # Compute flow statistics
    if flow is not None and len(flow) > 0:
        flow_magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
        flow_angle = np.arctan2(flow[..., 1], flow[..., 0])
        
        return {
            'flow': flow,
            'magnitude': flow_magnitude,
            'angle': flow_angle,
            'mean_magnitude': np.mean(flow_magnitude),
            'max_magnitude': np.max(flow_magnitude),
            'mean_angle': np.mean(flow_angle)
        }
    else:
        return {
            'flow': None,
            'magnitude': None,
            'angle': None,
            'mean_magnitude': 0,
            'max_magnitude': 0,
            'mean_angle': 0
        }


def visualize_event_frame(frame: np.ndarray, title: str = "Event Frame"):
    """
    Visualize an event frame.
    
    Args:
        frame: Event frame to visualize
        title: Plot title
    """
    plt.figure(figsize=(8, 6))
    if len(frame.shape) == 3:
        plt.imshow(frame)
    else:
        plt.imshow(frame, cmap='gray')
    plt.title(title)
    plt.axis('off')
    plt.show()
