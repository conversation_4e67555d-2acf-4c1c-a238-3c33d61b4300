#!/usr/bin/env python3
"""
ELOPE Quick Start Script
快速启动脚本，用于分阶段开发和性能跟踪
"""

import os
import sys
import subprocess

def main():
    """Main function for quick start."""
    print("="*60)
    print("🚀 ELOPE 快速开始")
    print("="*60)
    print("当前开发策略:")
    print("  开发集 (0-10): 快速迭代测试")
    print("  训练集 (0-27): 完整验证")  
    print("  测试集 (28-92): 最终提交")
    print("="*60)
    
    # Change to solution directory
    solution_dir = "elope_solution"
    if not os.path.exists(solution_dir):
        print(f"❌ 错误: 找不到 {solution_dir} 目录")
        return
    
    os.chdir(solution_dir)
    
    while True:
        print("\n选择操作:")
        print("1. 📊 记录/查看基线性能")
        print("2. ⚡ 快速优化 (开发集 0-10)")
        print("3. 🔍 开发集验证 (序列 0-10)")
        print("4. 📈 训练集验证 (序列 0-27)")
        print("5. 📝 记录新版本性能")
        print("6. 📋 查看性能历史")
        print("7. 🎯 生成最终提交")
        print("8. 🔧 参数优化向导")
        print("9. ❌ 退出")
        
        choice = input("\n请输入选择 (1-9): ").strip()
        
        if choice == "1":
            run_baseline_check()
        elif choice == "2":
            run_quick_optimization()
        elif choice == "3":
            run_dev_validation()
        elif choice == "4":
            run_train_validation()
        elif choice == "5":
            record_new_version()
        elif choice == "6":
            show_performance_history()
        elif choice == "7":
            generate_submission()
        elif choice == "8":
            parameter_optimization_wizard()
        elif choice == "9":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

def run_baseline_check():
    """Run baseline performance check."""
    print("\n📊 检查基线性能...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "from dev_framework import DevelopmentFramework; "
            "fw = DevelopmentFramework(); "
            "fw.record_baseline() if not fw.performance_log['baseline_recorded'] else fw.show_performance_history()"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def run_quick_optimization():
    """Run quick optimization on dev set."""
    print("\n⚡ 启动快速优化...")
    try:
        subprocess.run([sys.executable, "quick_optimize.py"])
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def run_dev_validation():
    """Run validation on dev set."""
    print("\n🔍 开发集验证 (序列 0-10)...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "from dev_framework import DevelopmentFramework; "
            "fw = DevelopmentFramework(); "
            "result = fw.validate_dev_set(); "
            "print(f'开发集分数: {result[\"dev_score\"]:.6f}')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def run_train_validation():
    """Run validation on training set."""
    print("\n📈 训练集验证 (序列 0-27)...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "from dev_framework import DevelopmentFramework; "
            "fw = DevelopmentFramework(); "
            "result = fw.validate_train_set(); "
            "print(f'训练集分数: {result[\"train_score\"]:.6f}')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def record_new_version():
    """Record new version performance."""
    version_name = input("输入版本名称: ").strip()
    if not version_name:
        print("❌ 版本名称不能为空")
        return
    
    description = input("输入版本描述 (可选): ").strip()
    
    print(f"\n📝 记录版本: {version_name}...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            f"from dev_framework import DevelopmentFramework; "
            f"fw = DevelopmentFramework(); "
            f"fw.record_version('{version_name}', '{description}')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def show_performance_history():
    """Show performance history."""
    print("\n📋 性能历史...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "from dev_framework import DevelopmentFramework; "
            "fw = DevelopmentFramework(); "
            "fw.show_performance_history()"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def generate_submission():
    """Generate final submission."""
    print("\n🎯 生成最终提交...")
    
    confirm = input("确认要生成测试集提交吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 取消提交生成")
        return
    
    output_file = input("输入输出文件名 (默认: submission.json): ").strip()
    if not output_file:
        output_file = "submission.json"
    
    try:
        result = subprocess.run([
            sys.executable, "-c",
            f"from dev_framework import DevelopmentFramework; "
            f"fw = DevelopmentFramework(); "
            f"fw.generate_test_submission('{output_file}')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def parameter_optimization_wizard():
    """Parameter optimization wizard."""
    print("\n🔧 参数优化向导...")
    print("1. 光流参数优化")
    print("2. 时间窗口优化")
    print("3. 运动估计参数优化")
    print("4. 渐进式全面优化")
    
    choice = input("选择优化类型 (1-4): ").strip()
    
    optimization_map = {
        "1": "1",  # optical flow
        "2": "2",  # time window
        "3": "3",  # motion estimation
        "4": "4"   # progressive
    }
    
    if choice in optimization_map:
        try:
            # Run quick_optimize.py with the choice
            process = subprocess.Popen([sys.executable, "quick_optimize.py"], 
                                     stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, 
                                     text=True)
            
            stdout, stderr = process.communicate(input=optimization_map[choice])
            
            if process.returncode == 0:
                print(stdout)
            else:
                print(f"❌ 错误: {stderr}")
        except Exception as e:
            print(f"❌ 执行错误: {e}")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
