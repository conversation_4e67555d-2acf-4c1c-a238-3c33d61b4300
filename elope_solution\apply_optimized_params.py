#!/usr/bin/env python3
"""
Apply optimized parameters and validate on training set.
"""

import json
import time
from dev_framework import DevelopmentFramework


def load_optimization_results():
    """Load optimization results from JSON file."""
    with open('quick_optimization_results.json', 'r') as f:
        return json.load(f)


def apply_optimized_parameters(framework, optimization_results):
    """Apply the best parameters found during optimization."""
    print("🔧 应用优化参数...")
    
    # Apply optical flow parameters
    optical_flow_params = None
    time_window_params = None
    motion_estimation_params = None
    
    for step in optimization_results['steps']:
        if step['step'] == 'optical_flow':
            optical_flow_params = step['best_params']
        elif step['step'] == 'time_window':
            time_window_params = step['best_params']
        elif step['step'] == 'motion_estimation':
            motion_estimation_params = step['best_params']
    
    # Apply optical flow parameters
    if optical_flow_params:
        print(f"  📊 光流参数: {optical_flow_params}")
        flow_estimator = framework.solver.flow_estimator
        for param, value in optical_flow_params.items():
            if hasattr(flow_estimator, param):
                setattr(flow_estimator, param, value)
                print(f"    ✓ {param} = {value}")
    
    # Apply time window parameters
    if time_window_params:
        print(f"  ⏱️ 时间窗口参数: {time_window_params}")
        time_window = time_window_params['time_window']
        framework.solver.frame_generator.time_window = time_window
        print(f"    ✓ time_window = {time_window/1000000:.1f}s")
    
    # Apply motion estimation parameters
    if motion_estimation_params:
        print(f"  🎯 运动估计参数: {motion_estimation_params}")
        motion_estimator = framework.solver.motion_estimator
        for param, value in motion_estimation_params.items():
            if hasattr(motion_estimator, param):
                setattr(motion_estimator, param, value)
                print(f"    ✓ {param} = {value}")
    
    print("✅ 参数应用完成")


def validate_on_training_set(framework):
    """Validate optimized parameters on full training set."""
    print("\n📈 在训练集上验证优化参数...")
    print("=" * 60)
    
    start_time = time.time()
    
    # Validate on full training set (sequences 0-27)
    train_result = framework.validate_train_set()
    
    validation_time = time.time() - start_time
    
    print(f"\n🎯 训练集验证结果:")
    print(f"  📊 训练集分数: {train_result['train_score']:.6f}")
    print(f"  ⏱️ 验证时间: {validation_time:.1f}秒")
    
    return train_result


def main():
    """Main function to apply optimized parameters and validate."""
    print("🚀 应用优化参数并在训练集上验证")
    print("=" * 60)
    
    # Load optimization results
    print("📂 加载优化结果...")
    optimization_results = load_optimization_results()
    
    print(f"  📊 基线分数: {optimization_results['baseline_score']:.6f}")
    print(f"  🎯 优化后分数: {optimization_results['final_score']:.6f}")
    print(f"  📈 改进幅度: +{optimization_results['total_improvement']:.6f} ({optimization_results['improvement_percentage']:.2f}%)")
    
    # Initialize framework
    print("\n🔧 初始化开发框架...")
    framework = DevelopmentFramework()
    
    # Apply optimized parameters
    apply_optimized_parameters(framework, optimization_results)
    
    # Validate on training set
    train_result = validate_on_training_set(framework)
    
    # Save validation results
    validation_results = {
        'optimization_results': optimization_results,
        'training_validation': train_result,
        'timestamp': time.time()
    }
    
    with open('training_validation_results.json', 'w') as f:
        json.dump(validation_results, f, indent=2)
    
    print(f"\n💾 结果已保存到 training_validation_results.json")
    
    # Summary
    print(f"\n📋 总结:")
    print(f"  🔧 开发集优化: {optimization_results['baseline_score']:.6f} → {optimization_results['final_score']:.6f}")
    print(f"  📈 训练集验证: {train_result['train_score']:.6f}")
    
    if train_result['train_score'] < optimization_results['baseline_score']:
        improvement = optimization_results['baseline_score'] - train_result['train_score']
        print(f"  🎉 训练集改进: +{improvement:.6f}")
        print(f"  ✅ 优化成功！建议生成新的提交文件")
    else:
        print(f"  ⚠️ 训练集分数未改善，需要进一步调优")


if __name__ == "__main__":
    main()
