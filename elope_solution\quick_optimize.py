#!/usr/bin/env python3
"""
Quick optimization script using development set (sequences 0-10).
Fast iteration for parameter tuning and algorithm testing.
"""

import sys
import os
import json
import numpy as np
import time
from typing import Dict, List, Any
import itertools

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dev_framework import DevelopmentFramework

class QuickOptimizer:
    """
    Quick optimization using development set for fast iteration.
    """
    
    def __init__(self):
        """Initialize quick optimizer."""
        self.framework = DevelopmentFramework()
        self.dev_sequences = self.framework.dev_sequences  # 0-10
        
    def test_optical_flow_parameters(self) -> Dict[str, Any]:
        """Test different optical flow parameter combinations."""
        print("Testing optical flow parameters on dev set...")
        
        # Define parameter ranges (smaller for quick testing)
        param_combinations = [
            # Current default-like
            {'maxCorners': 100, 'qualityLevel': 0.05, 'minDistance': 15},
            # More features
            {'maxCorners': 200, 'qualityLevel': 0.03, 'minDistance': 10},
            # Higher quality
            {'maxCorners': 150, 'qualityLevel': 0.01, 'minDistance': 12},
            # Conservative
            {'maxCorners': 80, 'qualityLevel': 0.08, 'minDistance': 20},
            # Aggressive
            {'maxCorners': 300, 'qualityLevel': 0.02, 'minDistance': 8},
        ]
        
        results = []
        
        for i, params in enumerate(param_combinations):
            print(f"\nTesting combination {i+1}/{len(param_combinations)}: {params}")
            
            # Update solver parameters
            self._update_optical_flow_params(params)
            
            # Quick validation on dev set
            start_time = time.time()
            dev_score = self.framework._validate_on_sequences(self.dev_sequences, "dev")
            test_time = time.time() - start_time
            
            result = {
                'parameters': params,
                'dev_score': dev_score,
                'test_time': test_time
            }
            results.append(result)
            
            print(f"  Dev score: {dev_score:.6f} (in {test_time:.1f}s)")
        
        # Find best result
        best_result = min(results, key=lambda x: x['dev_score'])
        
        print("\n" + "="*60)
        print("OPTICAL FLOW PARAMETER TEST RESULTS")
        print("="*60)
        for i, result in enumerate(sorted(results, key=lambda x: x['dev_score'])):
            marker = "🏆" if result == best_result else "  "
            print(f"{marker} {i+1}. Score: {result['dev_score']:.6f} | {result['parameters']}")
        print("="*60)
        
        return {'results': results, 'best': best_result}
    
    def test_time_window_parameters(self) -> Dict[str, Any]:
        """Test different time window parameters."""
        print("Testing time window parameters on dev set...")
        
        # Define time window combinations (in microseconds)
        time_windows = [
            500000,   # 0.5 seconds
            750000,   # 0.75 seconds  
            1000000,  # 1.0 seconds (current)
            1500000,  # 1.5 seconds
            2000000,  # 2.0 seconds
        ]
        
        results = []
        
        for i, time_window in enumerate(time_windows):
            print(f"\nTesting time window {i+1}/{len(time_windows)}: {time_window/1000000:.1f}s")
            
            # Update time window parameter
            self.framework.solver.frame_generator.time_window = time_window
            
            # Quick validation on dev set
            start_time = time.time()
            dev_score = self.framework._validate_on_sequences(self.dev_sequences, "dev")
            test_time = time.time() - start_time
            
            result = {
                'time_window': time_window,
                'time_window_seconds': time_window / 1000000,
                'dev_score': dev_score,
                'test_time': test_time
            }
            results.append(result)
            
            print(f"  Dev score: {dev_score:.6f} (in {test_time:.1f}s)")
        
        # Find best result
        best_result = min(results, key=lambda x: x['dev_score'])
        
        print("\n" + "="*60)
        print("TIME WINDOW PARAMETER TEST RESULTS")
        print("="*60)
        for i, result in enumerate(sorted(results, key=lambda x: x['dev_score'])):
            marker = "🏆" if result == best_result else "  "
            print(f"{marker} {i+1}. Score: {result['dev_score']:.6f} | {result['time_window_seconds']:.1f}s")
        print("="*60)
        
        return {'results': results, 'best': best_result}
    
    def test_motion_estimation_parameters(self) -> Dict[str, Any]:
        """Test different motion estimation parameters."""
        print("Testing motion estimation parameters on dev set...")
        
        # Define parameter combinations
        param_combinations = [
            # Current default-like
            {'ransac_threshold': 1.0, 'confidence': 0.99},
            # More conservative
            {'ransac_threshold': 1.5, 'confidence': 0.999},
            # More aggressive
            {'ransac_threshold': 0.5, 'confidence': 0.95},
            # Balanced
            {'ransac_threshold': 0.8, 'confidence': 0.98},
            # Very conservative
            {'ransac_threshold': 2.0, 'confidence': 0.999},
        ]
        
        results = []
        
        for i, params in enumerate(param_combinations):
            print(f"\nTesting combination {i+1}/{len(param_combinations)}: {params}")
            
            # Update motion estimation parameters
            self._update_motion_estimation_params(params)
            
            # Quick validation on dev set
            start_time = time.time()
            dev_score = self.framework._validate_on_sequences(self.dev_sequences, "dev")
            test_time = time.time() - start_time
            
            result = {
                'parameters': params,
                'dev_score': dev_score,
                'test_time': test_time
            }
            results.append(result)
            
            print(f"  Dev score: {dev_score:.6f} (in {test_time:.1f}s)")
        
        # Find best result
        best_result = min(results, key=lambda x: x['dev_score'])
        
        print("\n" + "="*60)
        print("MOTION ESTIMATION PARAMETER TEST RESULTS")
        print("="*60)
        for i, result in enumerate(sorted(results, key=lambda x: x['dev_score'])):
            marker = "🏆" if result == best_result else "  "
            print(f"{marker} {i+1}. Score: {result['dev_score']:.6f} | {result['parameters']}")
        print("="*60)
        
        return {'results': results, 'best': best_result}
    
    def progressive_optimization(self) -> Dict[str, Any]:
        """
        Progressive optimization on dev set:
        1. Test optical flow parameters
        2. Test time window parameters  
        3. Test motion estimation parameters
        4. Record best combination
        """
        print("Starting progressive optimization on dev set...")
        
        # Record baseline
        baseline_score = self.framework._validate_on_sequences(self.dev_sequences, "dev")
        print(f"Baseline dev score: {baseline_score:.6f}")
        
        optimization_log = {
            'baseline_score': baseline_score,
            'steps': []
        }
        
        # Step 1: Optical flow parameters
        print("\n" + "="*50)
        print("STEP 1: Optical Flow Parameters")
        print("="*50)
        flow_results = self.test_optical_flow_parameters()
        best_flow = flow_results['best']
        
        # Apply best optical flow parameters
        self._update_optical_flow_params(best_flow['parameters'])
        
        step1_improvement = baseline_score - best_flow['dev_score']
        optimization_log['steps'].append({
            'step': 'optical_flow',
            'best_params': best_flow['parameters'],
            'score': best_flow['dev_score'],
            'improvement': step1_improvement
        })
        
        print(f"Step 1 improvement: {step1_improvement:+.6f}")
        
        # Step 2: Time window parameters
        print("\n" + "="*50)
        print("STEP 2: Time Window Parameters")
        print("="*50)
        time_results = self.test_time_window_parameters()
        best_time = time_results['best']
        
        # Apply best time window
        self.framework.solver.frame_generator.time_window = best_time['time_window']
        
        step2_improvement = best_flow['dev_score'] - best_time['dev_score']
        optimization_log['steps'].append({
            'step': 'time_window',
            'best_params': {'time_window': best_time['time_window']},
            'score': best_time['dev_score'],
            'improvement': step2_improvement
        })
        
        print(f"Step 2 improvement: {step2_improvement:+.6f}")
        
        # Step 3: Motion estimation parameters
        print("\n" + "="*50)
        print("STEP 3: Motion Estimation Parameters")
        print("="*50)
        motion_results = self.test_motion_estimation_parameters()
        best_motion = motion_results['best']
        
        # Apply best motion estimation parameters
        self._update_motion_estimation_params(best_motion['parameters'])
        
        step3_improvement = best_time['dev_score'] - best_motion['dev_score']
        optimization_log['steps'].append({
            'step': 'motion_estimation',
            'best_params': best_motion['parameters'],
            'score': best_motion['dev_score'],
            'improvement': step3_improvement
        })
        
        print(f"Step 3 improvement: {step3_improvement:+.6f}")
        
        # Final validation
        final_score = self.framework._validate_on_sequences(self.dev_sequences, "dev")
        total_improvement = baseline_score - final_score
        improvement_percentage = (total_improvement / baseline_score) * 100
        
        optimization_log['final_score'] = final_score
        optimization_log['total_improvement'] = total_improvement
        optimization_log['improvement_percentage'] = improvement_percentage
        
        # Save results
        with open('quick_optimization_results.json', 'w') as f:
            json.dump(optimization_log, f, indent=2)
        
        # Print summary
        print("\n" + "="*70)
        print("PROGRESSIVE OPTIMIZATION SUMMARY (DEV SET)")
        print("="*70)
        print(f"Baseline Score:        {baseline_score:.6f}")
        print(f"Final Score:           {final_score:.6f}")
        print(f"Total Improvement:     {total_improvement:+.6f}")
        print(f"Improvement Percentage: {improvement_percentage:+.2f}%")
        print("\nStep-by-step improvements:")
        for i, step in enumerate(optimization_log['steps']):
            print(f"  {i+1}. {step['step']}: {step['improvement']:+.6f}")
        print("="*70)
        
        return optimization_log
    
    def _update_optical_flow_params(self, params: Dict[str, Any]):
        """Update optical flow parameters."""
        flow_estimator = self.framework.solver.motion_estimator.optical_flow_estimator
        for param, value in params.items():
            if hasattr(flow_estimator, param):
                setattr(flow_estimator, param, value)
    
    def _update_motion_estimation_params(self, params: Dict[str, Any]):
        """Update motion estimation parameters."""
        motion_estimator = self.framework.solver.motion_estimator
        for param, value in params.items():
            if hasattr(motion_estimator, param):
                setattr(motion_estimator, param, value)

def main():
    """Main function for quick optimization."""
    print("ELOPE Quick Optimization (Dev Set 0-10)")
    print("="*50)
    
    optimizer = QuickOptimizer()
    
    # Choose optimization type
    choice = input("Choose optimization:\n"
                  "1. Optical flow parameters only\n"
                  "2. Time window parameters only\n"
                  "3. Motion estimation parameters only\n"
                  "4. Progressive optimization (all)\n"
                  "5. Quick baseline check\n"
                  "Enter choice (1-5): ").strip()
    
    if choice == "1":
        optimizer.test_optical_flow_parameters()
    elif choice == "2":
        optimizer.test_time_window_parameters()
    elif choice == "3":
        optimizer.test_motion_estimation_parameters()
    elif choice == "4":
        optimizer.progressive_optimization()
    elif choice == "5":
        score = optimizer.framework._validate_on_sequences(optimizer.dev_sequences, "dev")
        print(f"Current dev set score: {score:.6f}")
    else:
        print("Invalid choice. Running progressive optimization...")
        optimizer.progressive_optimization()
    
    print("\nQuick optimization completed!")

if __name__ == "__main__":
    main()
